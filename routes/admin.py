import os
from flask import render_template, request, redirect, url_for, flash, session, jsonify, send_from_directory
from werkzeug.utils import secure_filename
from datetime import datetime
import re
import json
import markdown
import time
from json_repair import repair_json
from models import db, Subject, Question, QuestionPrerequisite, QuestionRelevance, Topic, Part, ProblemSet, MarkingPoint, Attachment, Option, Submission, NotesChunk, QuestionNotesRelevance, User
from .utils import admin_required, login_required, allowed_file, ALLOWED_DATA_EXTENSIONS, ALLOWED_ATTACHMENTS, error_logger, app_logger
# from pdftomd import PDFProcessor
from pdftomd_test import PDFProcessor
from .ai_helpers import generate_marking_points
from validate_forms import validate_ocr_review_form
from concurrent.futures import ThreadPoolExecutor
from helper import process_csv_file, process_excel_file
import csv

def update_current_question(question_id):
    original_parts_for_current_question = session['question_data'][question_id]
    processed_parts_from_form = []

    part_form_idx = 0
    while request.form.get(f"description-{part_form_idx}") is not None:
        part_data = {}

        # Preserve type and existing attachments structure from original part data if it exists
        if part_form_idx < len(original_parts_for_current_question):
            original_part_data = original_parts_for_current_question[part_form_idx]
            part_data['type'] = original_part_data.get('type', 'text')
            # Ensure attachments is a dict, even if it was None or empty list before
            part_data['attachments'] = original_part_data.get('attachments') if isinstance(original_part_data.get('attachments'), dict) else {}
        else:
            # New part added on the client side
            part_data['type'] = 'text' # New parts default to text
            part_data['attachments'] = {} # New parts start with no attachments

        # Update description
        part_data['description'] = request.form.get(f"description-{part_form_idx}")

        # Update score
        score_str = request.form.get(f"score-{part_form_idx}")
        try:
            score = int(score_str)
            if score <= 0:
                score = 1
        except (ValueError, TypeError):
            score = 1
        part_data['score'] = score

        # Update answer and MCQ options based on type
        part_type = part_data.get('type', 'text')
        if part_type == 'mcq':
            part_data['answer'] = request.form.get(f"part-{part_form_idx}-correct-answer")

            options_list = []
            opt_idx = 0
            while True:
                opt_text = request.form.get(f"part-{part_form_idx}-option-{opt_idx}-text")
                if opt_text is None:
                    break
                options_list.append({'text': opt_text})
                opt_idx += 1
            part_data['options'] = options_list
        else: # 'text' or other types
            part_data['answer'] = request.form.get(f"answer-{part_form_idx}")
            # Ensure 'options' field exists and is empty for non-MCQ parts for consistency
            part_data['options'] = []


        # Update attachments visibility
        # part_data['attachments'] should be a dict like {'filename.jpg': True/False}
        if part_data.get('attachments'): # Check if attachments dict exists
            current_attachments_on_part = part_data['attachments']
            updated_attachments_visibility = {}
            for attachment_filename in current_attachments_on_part.keys():
                checkbox_name = f"{attachment_filename}-{part_form_idx}"
                is_visible = request.form.get(checkbox_name) # Checkbox value is 'on' or None
                updated_attachments_visibility[attachment_filename] = True if is_visible else False
            part_data['attachments'] = updated_attachments_visibility
        else: # Ensure attachments key exists, even if empty (e.g. for new parts)
             part_data['attachments'] = {}

        # Update marking points
        updated_marking_points = []
        mp_idx = 0
        while True:
            mp_description = request.form.get(f"marking-points-{part_form_idx}-{mp_idx}-description")
            mp_score_str = request.form.get(f"marking-points-{part_form_idx}-{mp_idx}-score")

            if mp_description is None and mp_score_str is None: # No more marking points for this part
                break

            # Ensure we capture even if one is None, though ideally both should exist or not
            if mp_description is not None : # Only add if description exists
                 updated_marking_points.append({
                     'description': mp_description or "", # Default to empty string if None
                     'score': mp_score_str or "0" # Default to "0" if None
                 })
            mp_idx += 1
        part_data['marking_points'] = updated_marking_points

        processed_parts_from_form.append(part_data)
        part_form_idx += 1

    session['question_data'][question_id] = processed_parts_from_form

    # Update topic and source for the question
    if request.form.get("topic_id") is not None:
        session['topic_id'][question_id] = request.form.get("topic_id")
        # Ensure topic_id is not empty string before querying
        if session['topic_id'][question_id]:
            topic_obj = Topic.query.filter_by(id=session['topic_id'][question_id]).first()
            session['topic_name'][question_id] = topic_obj.name if topic_obj else None
        else: # No topic selected
            session['topic_name'][question_id] = None
            session['topic_id'][question_id] = None # Ensure it's None, not empty string

    session['source'][question_id] = request.form.get("source")

    session.modified = True

def init_ocr_review():
    structured_data = session.get('structured_data')
    subject_id = session.get('subject_id')

    # if not structured_data or not subject_id: # for testing
    #     json_path = os.path.join('uploads', "structured_data.json")
    #     with open(json_path, 'r') as json_file:
    #         structured_data = json.load(json_file)

    #     if not subject_id:
    #         session['subject_id'] = 3 # placeholder

    questions_map = {}
    for question in structured_data:
        if int(question['number'])-1 not in questions_map:
            questions_map[int(question['number'])-1] = [question]
        else:
            questions_map[int(question['number'])-1].append(question)

    questions_list = []
    for number, questions in questions_map.items():
        questions_list.append(questions)

    for question_group in questions_list: # Iterate through groups of parts for a "question number"
        for part_data in question_group: # Each item from OCR is a "part" or sub-question
            # Set default score
            score_val = part_data.get('score')
            try:
                # Attempt to convert to int, ensuring it's a positive value
                parsed_score = int(score_val)
                part_data['score'] = parsed_score if parsed_score > 0 else 1
            except (ValueError, TypeError, AttributeError):
                part_data['score'] = 1 # Default to 1 if score is missing, not a number, or not positive

            # Ensure 'type' and 'options' fields exist for template rendering
            part_data['type'] = part_data.get('type', 'text') # Default to 'text' if not provided by OCR

            if part_data['type'] == 'mcq':
                # Ensure options is a list, default to empty if not provided or not a list
                options_val = part_data.get('options')
                part_data['options'] = options_val if isinstance(options_val, list) else []
            else:
                part_data['options'] = [] # Non-MCQ parts should have an empty list of options

            # Process attachments: convert list of filenames to a dict for the template
            attachments_data = part_data.get('attachments') # Expected to be a list of filenames
            processed_attachments = {}
            if isinstance(attachments_data, list):
                for filename in attachments_data:
                    if isinstance(filename, str): # Ensure filename is a string
                        processed_attachments[filename] = True # Mark as 'on' (checked) by default for review
            part_data['attachments'] = processed_attachments

    session['question_data'] = questions_list
    session['topic_id'] = [None for _ in range(len(questions_list))]
    session['topic_name'] = [None for _ in range(len(questions_list))]
    session['source'] = [None for _ in range(len(questions_list))]
    session['errors'] = []
    session['warnings'] = []
    session.modified = True

    import pprint
    pprint.pprint(session)

def register_admin_routes(app, db, session):

    upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')

    @app.route("/admin", methods=["GET"])
    @admin_required
    def admin():
        """Admin dashboard page."""
        try:
            subjects = Subject.query.order_by(Subject.name).all()
            return render_template("admin.html", title="Admin", subjects=subjects)
        except Exception as e:
            error_logger.exception("Error loading admin dashboard")
            flash("Error loading admin page.", "error")
            return redirect(url_for('index'))

    @app.route("/superadmin", methods=["GET"])
    @admin_required # Consider if a separate 'superadmin' role/check is needed
    def superadmin():
        """Superadmin page (potentially for adding subjects/topics)."""
        try:
            subjects = Subject.query.order_by(Subject.name).all()
            return render_template("superadmin.html", title="Superadmin", subjects=subjects)
        except Exception as e:
            error_logger.exception("Error loading superadmin dashboard")
            flash("Error loading superadmin page.", "error")
            return redirect(url_for('admin')) # Redirect to admin dashboard

    @app.route('/add_subject', methods=['POST'])
    @admin_required # Or superadmin_required if distinct
    def add_subject():
        """Adds a new subject and its topics."""
        name = request.form.get('new_subject')
        syllabus = request.form.get('syllabus')
        topics_raw = request.form.get('topics', '')

        if not name or not syllabus:
            flash("Subject name and syllabus are required.", "error")
            return redirect(url_for('superadmin'))

        # Basic validation for existing subject?
        if Subject.query.filter_by(name=name, syllabus=syllabus).first():
             flash(f"Subject '{name}' with syllabus '{syllabus}' already exists.", "warning")
             return redirect(url_for('superadmin'))

        try:
            new_subject = Subject(name=name, syllabus=syllabus)
            db.session.add(new_subject)
            db.session.flush() # Flush to get the new_subject.id

            topic_names = [t.strip() for t in topics_raw.split(',') if t.strip()]
            if topic_names:
                for topic_name in topic_names:
                    # Check if topic already exists for this subject? Maybe allow duplicates?
                    topic = Topic(name=topic_name, subject_id=new_subject.id)
                    db.session.add(topic)

            db.session.commit()
            flash(f"Subject '{name}' and its topics added successfully!", "success")
            app_logger.info(f"Admin {session.get('username')} added subject: {name} ({syllabus})")
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error adding subject '{name}': {e}")
            flash(f"Error adding subject: {str(e)}", "error")

        return redirect(url_for('superadmin'))


    @app.route('/add_question', methods=['POST'])
    @admin_required
    def add_question():
        """Adds a new question with its parts."""
        try:
            # Extract form data
            # subject_id = request.form.get('subject_id') # Subject not directly linked? Use topic_id
            title = request.form.get('title')
            topic_id = request.form.get('topic_id')
            description = request.form.get('description', '')
            num_parts = int(request.form.get('num_parts', 0))
            source = request.form.get('source', '')
            question_attachment_file = request.files.get('attachment') # Use .get for optional file

            if not title or not topic_id or num_parts <= 0:
                flash("Title, Topic, and at least one part are required.", "error")
                return redirect(url_for('admin')) # Redirect back to admin dashboard

            # Validate topic exists
            topic = Topic.query.get(topic_id)
            if not topic:
                 flash("Selected topic not found.", "error")
                 return redirect(url_for('admin'))

            # Handle question attachment
            question_att_filename = None
            if question_attachment_file and allowed_file(question_attachment_file.filename, ALLOWED_ATTACHMENTS):
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                question_att_filename = secure_filename(f"q_att_{timestamp}_{question_attachment_file.filename}")
                try:
                    question_attachment_file.save(os.path.join(upload_folder, question_att_filename))
                except Exception as e:
                     error_logger.exception(f"Error saving question attachment {question_att_filename}: {e}")
                     flash("Error saving question attachment.", "error")
                     question_att_filename = None # Reset filename if save fails

            # Create Question object
            new_question = Question(
                title=title,
                description=description,
                topic_id=topic_id,
                source=source,
                comments=request.form.get('comments', ''),
                attachment=question_att_filename
            )
            db.session.add(new_question)
            db.session.flush() # Get the new_question.id

            # Add Parts
            for i in range(num_parts):
                part_description = request.form.get(f'part_{i}')
                part_answer = request.form.get(f'answer_{i}')
                part_score_str = request.form.get(f'score_{i}')
                part_attachment_file = request.files.get(f'attachment_{i}') # Use .get
                part_input_type = request.form.get(f'input_type_{i}')

                if part_input_type == 'mcq':
                    mcq_options = request.form.getlist(f'mcq_option_{i}[]')
                    mcq_correct = request.form.get(f'mcq_correct_{i}')
                    part_answer = mcq_correct
                    print("LOG")
                    print(part_answer, mcq_correct)

                print(f"part_input_type: {part_input_type}")
                print(f"mcq_correct: {mcq_correct}")
                print(f"part_description: {part_description}")
                print(f"part_answer: {part_answer}")
                print(f"part_score_str: {part_score_str}")

                # Basic validation for part data
                if not part_description or not part_answer or not part_score_str:
                    flash(f"Part {i+1}: Description, Answer, and Score are required.", "warning")
                    continue # Skip this part if essential data is missing

                try:
                    part_score = int(part_score_str)
                except ValueError:
                    flash(f"Part {i+1}: Invalid score value '{part_score_str}'. Must be an integer.", "warning")
                    continue # Skip part with invalid score

                # Part attachment will be handled after creating the part

                part = Part(
                    description=part_description,
                    question_id=new_question.id,
                    answer=part_answer,
                    score=part_score,
                    comments=request.form.get(f'comments_{i}', ''),
                    input_type=part_input_type
                )
                db.session.add(part)
                db.session.flush()  # Get the new part.id

                if part_input_type == 'mcq':
                    for j, option in enumerate(mcq_options):
                        new_option = Option(
                            part_id=part.id,
                            description=option,
                            is_correct=(j==int(mcq_correct))
                        )
                        db.session.add(new_option)

                # Handle part attachment
                if part_attachment_file and allowed_file(part_attachment_file.filename, ALLOWED_ATTACHMENTS):
                    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                    part_att_filename = secure_filename(f"p_att_{timestamp}_{part_attachment_file.filename}")
                    try:
                        part_attachment_file.save(os.path.join(upload_folder, part_att_filename))
                        # Create attachment record
                        attachment = Attachment(
                            filename=part_att_filename,
                            part_id=part.id
                        )
                        db.session.add(attachment)
                    except Exception as e:
                        error_logger.exception(f"Error saving part attachment {part_att_filename}: {e}")
                        flash(f"Error saving attachment for part {i+1}.", "error")

                # Handle marking points
                marking_points = request.form.getlist(f'marking_point_{i}[]')
                marking_scores = request.form.getlist(f'marking_score_{i}[]')

                if marking_points and marking_scores and len(marking_points) == len(marking_scores):
                    for j, (description, score_str) in enumerate(zip(marking_points, marking_scores)):
                        if description.strip() and score_str.strip():
                            try:
                                score = float(score_str)
                                marking_point = MarkingPoint(
                                    part_id=part.id,
                                    description=description,
                                    score=score,
                                    order=j
                                )
                                db.session.add(marking_point)
                            except ValueError:
                                flash(f"Part {i+1}, Marking Point {j+1}: Invalid score value '{score_str}'. Must be a number.", "warning")

            db.session.commit()
            flash(f"Question '{title}' added successfully!", "success")
            app_logger.info(f"Admin {session.get('username')} added question ID: {new_question.id}")
            return redirect(url_for('admin'))

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error adding question: {e}")
            flash(f"An unexpected error occurred while adding the question: {str(e)}", "error")
            return redirect(url_for('admin'))


    @app.route("/bulk_upload", methods=["GET", "POST"])
    @admin_required
    def bulk_upload():
        """Handles bulk upload via CSV/Excel or PDF OCR."""
        all_subjects = Subject.query.order_by(Subject.name).all()

        if request.method == "POST":
            subject_id = request.form.get('subject_id')

            if not subject_id:
                flash("Please select a subject.", "error")
                return redirect(request.url)

            # --- PDF OCR Upload ---
            if 'question_pdf' in request.files and request.files['question_pdf'].filename:
                question_file = request.files['question_pdf']

                # Validate file type
                if not allowed_file(question_file.filename, {'pdf'}):
                    flash('Only PDF files are allowed for questions.', 'error')
                    return redirect(request.url)

                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

                # Save question PDF securely
                question_filename = secure_filename(f"q_pdf_{timestamp}_{question_file.filename}")
                question_path = os.path.join(upload_folder, question_filename)
                try:
                    question_file.save(question_path)
                    app_logger.info(f"Saved PDF for OCR: {question_path}")
                except Exception as e:
                    error_logger.exception(f"Error saving PDF {question_filename}: {e}")
                    flash("Error saving PDF file.", "error")
                    return redirect(request.url)

                # --- Process PDF ---
                try:
                    pdf_processor = PDFProcessor() # Initialize your PDF processor

                    START_TIME = time.time()
                    # Process the single PDF containing both questions and answers
                    structured_data = pdf_processor.get_structured_data(question_path)
                    session['structured_data'] = structured_data

                    # Save structured_data to a JSON file
                    json_filename = f"structured_data.json"
                    json_path = os.path.join(upload_folder, json_filename)
                    with open(json_path, 'w') as json_file:
                        json.dump(structured_data, json_file)
                    app_logger.info(f"Structured data saved to {json_path}")

                    session['subject_id'] = subject_id
                    # session.modified = True # Ensure session is saved # This will be set later

                    init_ocr_review() # This populates session['question_data']

                    # --- START: Auto-generate marking points in parallel ---
                    all_parts_to_process = []
                    # session['question_data'] is a list of question_groups,
                    # each question_group is a list of part_data dicts.
                    for question_group_idx, question_group in enumerate(session.get('question_data', [])):
                        for part_idx, part_data in enumerate(question_group):
                            all_parts_to_process.append({
                                'description': part_data.get('description', ''),
                                'answer': part_data.get('answer', ''),
                                'score': part_data.get('score', 1), # Score should be set by init_ocr_review
                                'session_indices': (question_group_idx, part_idx) # To map results back
                            })

                    def generate_for_part_task(part_info):
                        try:
                            # Ensure score is an int for generate_marking_points
                            score_val = int(part_info['score'])
                            if score_val <= 0:
                                score_val = 1

                            # Call the imported generate_marking_points function
                            marking_points_list = generate_marking_points(
                                part_info['description'],
                                part_info['answer'],
                                score_val
                            )
                            return part_info['session_indices'], marking_points_list
                        except Exception as e:
                            error_logger.error(f"Error generating marking points for part at session indices {part_info['session_indices']}: {e}")
                            return part_info['session_indices'], [] # Return empty list on error

                    generated_marking_points_results = []
                    if all_parts_to_process:
                        # Adjust max_workers based on API limits and server capacity.
                        # Using a conservative number like 5-10 is often a good start for external API calls.
                        num_workers = min(10, len(all_parts_to_process))
                        with ThreadPoolExecutor(max_workers=num_workers) as executor:
                            results_iterator = executor.map(generate_for_part_task, all_parts_to_process)
                            generated_marking_points_results = list(results_iterator)

                    for indices, marking_points in generated_marking_points_results:
                        qg_idx, p_idx = indices
                        session['question_data'][qg_idx][p_idx]['marking_points'] = marking_points

                    session.modified = True # Ensure session is saved after all updates
                    # --- END: Auto-generate marking points ---

                    END_TIME = time.time() # Original position of END_TIME

                    app_logger.info(f"PDF processed for subject {subject_id}. Marking points pre-generated. Redirecting to review.")
                    flash(f"Documents processed in {(END_TIME - START_TIME):.1f} seconds. Marking points have been pre-generated for review.", "info") # Updated flash message

                    return redirect(url_for('review_ocr_questions'))

                except Exception as e:
                    error_logger.exception(f"Error during PDF processing: {e}")
                    flash(f"Error processing PDF files: {str(e)}", "error")
                    return redirect(request.url)

            # --- CSV/Excel Upload ---
            elif 'upload_file' in request.files and request.files['upload_file'].filename:
                upload_file = request.files['upload_file']
                if not allowed_file(upload_file.filename, ALLOWED_DATA_EXTENSIONS):
                    flash('Only CSV and Excel files are allowed for data upload.', 'error')
                    return redirect(request.url)

                # Get topic_id from form data
                topic_id = request.form.get('topic_id')
                if not topic_id:
                    flash('Please select a topic before uploading the file.', 'error')
                    return redirect(request.url)

                try:
                    # Save the uploaded file temporarily
                    filename = secure_filename(upload_file.filename)
                    file_path = os.path.join(upload_folder, filename)
                    upload_file.save(file_path)

                    # Process the file based on its extension
                    if upload_file.filename.lower().endswith('.csv'):
                        questions_data = process_csv_file(file_path, topic_id)
                    else:
                        questions_data = process_excel_file(file_path, topic_id)

                    # Create questions and parts in the database
                    num_questions = 0
                    for question_data in questions_data:
                        # Create the question
                        new_question = Question(
                            title=question_data['title'],
                            description=question_data['description'],
                            topic_id=topic_id,
                            source=question_data.get('source', ''),
                            comments=question_data.get('comments', '')
                        )
                        db.session.add(new_question)
                        db.session.flush()  # Get the question ID

                        # Create parts for this question
                        for part_data in question_data['parts']:
                            new_part = Part(
                                description=part_data['description'],
                                question_id=new_question.id,
                                answer=part_data['answer'],
                                score=part_data['score'],
                                comments=part_data.get('comments', ''),
                                input_type='saq'  # Default to short answer questions
                            )
                            db.session.add(new_part)
                            db.session.flush()  # Get the part ID

                            # Create marking points if provided
                            for mp_data in part_data.get('marking_points', []):
                                new_marking_point = MarkingPoint(
                                    part_id=new_part.id,
                                    description=mp_data['description'],
                                    score=mp_data['score'],
                                    order=len(new_part.marking_points) + 1
                                )
                                db.session.add(new_marking_point)

                        num_questions += 1

                    db.session.commit()

                    # Clean up the temporary file
                    os.remove(file_path)

                    flash(f"Successfully uploaded {num_questions} questions from {upload_file.filename}!", "success")
                    app_logger.info(f"Admin {session.get('username')} uploaded {num_questions} questions from CSV/Excel file {upload_file.filename}")
                    return redirect(url_for('admin'))

                except Exception as e:
                    db.session.rollback()
                    # Clean up the temporary file if it exists
                    if 'file_path' in locals() and os.path.exists(file_path):
                        os.remove(file_path)
                    error_logger.exception(f"Error processing data file {upload_file.filename}: {e}")
                    flash(f"Error processing data file: {str(e)}", "error")
                    return redirect(request.url)

            else:
                flash("No file selected. Please upload a Question PDF or a CSV/Excel data file.", "error")
                return redirect(request.url)

        # GET request: render the upload form
        return render_template('bulk_upload.html', subjects=all_subjects)

    @app.route('/download_csv_template')
    @admin_required
    def download_csv_template():
        """Download the CSV template file for bulk upload."""
        try:
            template_path = os.path.join(app.static_folder, 'templates')
            return send_from_directory(
                template_path,
                'question_upload_template.csv',
                as_attachment=True,
                download_name='question_upload_template.csv'
            )
        except Exception as e:
            error_logger.exception(f"Error downloading CSV template: {e}")
            flash("Error downloading CSV template.", "error")
            return redirect(url_for('bulk_upload'))

    @app.route("/get_question_html/<int:question_id>", methods=["POST", "GET"])
    @admin_required
    def get_question_html(question_id):
        questions = session['question_data']
        topics = Topic.query.filter_by(subject_id=3).order_by(Topic.name).all()

        if request.method == 'POST':
            update_current_question(question_id)
            target_index = int(request.form.get('target_index', question_id))

            return render_template("_question_fragment.html",
                                   question_data=questions[target_index],
                                   cur_idx=target_index,
                                   max_idx=len(questions)-1,
                                   topics=topics,
                                   topic_id=session['topic_id'][target_index],
                                   topic_name=session['topic_name'][target_index],
                                   source=session['source'][target_index],
                                   errors=session['errors'],
                                   warnings=session['warnings']
                                )

        return render_template("_question_fragment.html",
                               question_data=questions[question_id],
                               cur_idx=question_id,
                               max_idx=len(session['question_data'])-1,
                               topics=topics,
                               topic_id=session['topic_id'][question_id],
                               topic_name=session['topic_name'][question_id],
                               source=session['source'][question_id],
                               errors=session['errors'],
                               warnings=session['warnings']
                               )

    @app.route("/get_marking_points", methods=["POST"])
    @admin_required
    def get_marking_points():
        question_id = int(request.form.get('question_id'))
        part_id = int(request.form.get('part_id'))

        part = session['question_data'][question_id][part_id]

        # Ensure score is an integer and at least 1
        score = part['score']
        try:
            score = int(score)
            if score <= 0:
                score = 1
        except (ValueError, TypeError):
            score = 1

        # Update the score in the session
        session['question_data'][question_id][part_id]['score'] = score

        # Generate marking points
        session['question_data'][question_id][part_id]['marking_points'] = generate_marking_points(
            part['description'],
            part['answer'],
            score
        )
        session.modified = True

        # Update the part reference to include the new marking points
        part = session['question_data'][question_id][part_id]
        return render_template("_marking_points.html", part=part, part_index=part_id)

    @app.route("/review_ocr_questions", methods=["POST", "GET"])
    @admin_required
    def review_ocr_questions():
        topics = Topic.query.filter_by(subject_id=session['subject_id']).order_by(Topic.name).all()
        return render_template("review_ocr_questions.html", question_data=session['question_data'][0],
                                                          cur_idx=0,
                                                          max_idx=len(session['question_data'])-1,
                                                          topics=topics,
                                                          warnings=session['warnings'],
                                                          errors=session['errors'])

    @app.route("/submit_ocr_data/<int:question_id>1", methods=["POST", "GET"])
    @admin_required
    def submit_ocr_data(question_id):
        num_questions = len(session['question_data'])
        update_current_question(question_id)

        errors, warnings, flag = validate_ocr_review_form(session)
        if flag:
            session['errors'] = errors
            session['warnings'] = warnings
            session.modified = True

            flash("Please fix the errors before submitting.", "error")
            return redirect(url_for('review_ocr_questions'))

        newly_created_questions = []
        for question_id, question in enumerate(session["question_data"]):
            new_qn = Question(
                title= f"Question {question_id+1}",
                description='',
                source= session['source'][question_id],
                comments='',
                attachment= None,
                topic_id= session['topic_id'][question_id]
            )

            db.session.add(new_qn)
            db.session.flush() # Get the new_question.id
            newly_created_questions.append(new_qn)

            for part in question:
                new_p = Part(
                    description= part['description'],
                    question_id= new_qn.id,
                    answer= part['answer'],
                    score= part['score'],
                    comments= part.get('comments', ''),
                    input_type= part.get('type', 'text') # Set input_type based on OCR data
                )
                db.session.add(new_p)
                db.session.flush() # Get the new_part.id

                # Handle MCQ options if present
                if new_p.input_type == 'mcq' and part.get('options'):
                    for option_data in part.get('options', []):
                        if isinstance(option_data, dict) and 'text' in option_data:
                            is_correct_option = (option_data['text'] == new_p.answer)
                            new_option = Option(
                                part_id=new_p.id,
                                description=option_data['text'],
                                is_correct=is_correct_option
                            )
                            db.session.add(new_option)
                    db.session.flush() # Ensure options are flushed before moving to next part

                if part.get('attachments'):
                    for attachment, save in part['attachments'].items():
                        if not save:
                            continue

                        app_logger.info(f"Saving attachment {attachment} for part {new_p.id}")
                        new_a = Attachment(
                            filename= attachment,
                            part_id= new_p.id
                        )
                        db.session.add(new_a)

                if 'marking_points' not in part:
                    continue

                for mp in part['marking_points']:
                    new_mp = MarkingPoint(
                        part_id= new_p.id,
                        description= mp['description'],
                        score= mp['score'],
                        order=0 # fix later
                    )
                    db.session.add(new_mp)

        problemset = None
        if newly_created_questions:
            try:
                timestamp_str = datetime.now().strftime("%Y-%m-%d %H:%M")
                problemset_name = f"OCR Upload - {timestamp_str}"
                problemset = ProblemSet(
                    name=problemset_name,
                    description=f"Generated from OCR upload on {timestamp_str}",
                    created_by=session['user_id'] # Ensure user_id is in session
                )
                problemset.questions.extend(newly_created_questions)
                db.session.add(problemset)
                app_logger.info(f"Creating ProblemSet '{problemset_name}' with {len(newly_created_questions)} questions.")
            except Exception as ps_err:
                error_logger.exception(f"Error creating ProblemSet for OCR upload: {ps_err}")
                flash("Error creating associated problem set.", "warning")
                # Don't rollback questions already saved

        # Commit all successful changes
        db.session.commit()
        app_logger.info(f"Admin {session.get('username')} saved {num_questions} questions from OCR review.")

        # Clear session data
        session.pop('structured_data', None)
        session.pop('subject_id', None)
        session.pop('topic_id', None)
        session.modified = True

        if problemset:
            flash(f"Successfully saved {num_questions} questions and created Problem Set '{problemset.name}'.", "success")
            return redirect(url_for('view_problemset', id=problemset.id))
        elif num_questions > 0:
            flash(f"Successfully saved {num_questions} questions.", "success")
            return redirect(url_for('vault')) # Redirect to admin dashboard
        else:
            flash("No questions were saved. Please check the review form or upload again.", "warning")
            return redirect(url_for('bulk_upload'))

    @app.route('/edit_question/<int:question_id>', methods=['GET', 'POST'])
    @admin_required
    def edit_question(question_id):
        """Edits an existing question and its parts/marking points."""
        question = Question.query.get_or_404(question_id)

        if request.method == 'POST':
            try:
                # Update question details
                question.title = request.form.get('title')
                question.description = request.form.get('description')
                question.source = request.form.get('source')
                question.comments = request.form.get('comments')
                new_topic_id = request.form.get('topic_id')

                # Validate topic ID
                if not Topic.query.get(new_topic_id):
                     flash("Invalid Topic selected.", "error")
                     # Avoid committing changes if topic is invalid
                     return redirect(url_for('edit_question', question_id=question_id))
                question.topic_id = new_topic_id

                # Handle question attachment update/removal
                # if request.form.get('remove_question_attachment'):
                #      # Optionally delete file from disk here if needed
                #      question.attachment = None
                # elif 'attachment' in request.files:
                #     attachment = request.files['attachment']
                #     if attachment and attachment.filename != '' and allowed_file(attachment.filename, ALLOWED_ATTACHMENTS):
                #         # Optionally delete old file from disk here
                #         timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                #         att_filename = secure_filename(f"q_att_{timestamp}_{attachment.filename}")
                #         attachment.save(os.path.join(upload_folder, att_filename))
                #         question.attachment = att_filename
                #     elif attachment and attachment.filename != '':
                #          flash("Invalid file type for question attachment.", "warning")


                # --- Update Parts and Marking Points ---
                part_ids_in_form = set()
                for key in request.form:
                     # Find part IDs from form field names like 'part_123_description'
                     match = re.match(r'part_(\d+)_description', key)
                     if match:
                         part_ids_in_form.add(int(match.group(1)))

                # Update existing parts and delete removed ones
                parts_to_delete = []
                for part in question.parts:
                    if part.id in part_ids_in_form:
                        # Update existing part
                        part.description = request.form.get(f'part_{part.id}_description', '')
                        part.comments = request.form.get(f'part_{part.id}_comments', '')
                        part.input_type = request.form.get(f'input_type_{part.id}', 'saq')

                        # Handle different input types
                        if part.input_type == 'mcq':
                            # Get MCQ options and correct answer
                            mcq_options = request.form.getlist(f'mcq_option_{part.id}[]')
                            mcq_correct_index = request.form.get(f'mcq_correct_{part.id}')

                            # Validate MCQ data
                            if not mcq_options or len(mcq_options) < 2:
                                flash(f"Part {part.id}: MCQ must have at least 2 options.", "warning")
                                mcq_options = ["Option 1", "Option 2"]  # Default options
                                mcq_correct_index = "0"  # Default correct answer

                            if not mcq_correct_index or not mcq_correct_index.isdigit() or int(mcq_correct_index) >= len(mcq_options):
                                flash(f"Part {part.id}: Invalid correct option selected. Setting to first option.", "warning")
                                mcq_correct_index = "0"  # Default to first option

                            # Set the answer to the correct option's index
                            part.answer = mcq_correct_index

                            # Delete existing options
                            Option.query.filter_by(part_id=part.id).delete()

                            # Create new options
                            for i, option_text in enumerate(mcq_options):
                                new_option = Option(
                                    part_id=part.id,
                                    description=option_text,
                                    is_correct=(i == int(mcq_correct_index))
                                )
                                db.session.add(new_option)
                        else:
                            # For SAQ, just update the answer field
                            part.answer = request.form.get(f'part_{part.id}_answer', '')

                            # Delete any existing MCQ options if the type was changed from MCQ to SAQ
                            if Option.query.filter_by(part_id=part.id).count() > 0:
                                Option.query.filter_by(part_id=part.id).delete()

                        # Update score
                        try:
                            part.score = int(request.form.get(f'part_{part.id}_score', 0))
                        except ValueError:
                             flash(f"Invalid score for part {part.id}. Setting to 0.", "warning")
                             part.score = 0

                        # Handle part attachments

                        # First, handle existing attachments (keep or delete based on checkbox)
                        # Store the filenames of existing attachments to check later
                        existing_attachment_filenames = []
                        newly_uploaded_filenames = []

                        # Get a list of existing attachments before we make any changes
                        existing_attachments = list(part.attachments)

                        # Process existing attachments
                        for attachment in existing_attachments:
                            # Check if the checkbox for this attachment is checked
                            checkbox_name = f"{attachment.filename}-{part.id}"
                            if checkbox_name in request.form:
                                # Checkbox is checked, keep this attachment
                                existing_attachment_filenames.append(attachment.filename)
                                app_logger.info(f"Keeping attachment {attachment.filename} for part {part.id}")
                            else:
                                # Checkbox is not checked, delete this attachment
                                # Optionally delete the file from disk here
                                # os.remove(os.path.join(upload_folder, attachment.filename))
                                db.session.delete(attachment)
                                app_logger.info(f"Deleted attachment {attachment.filename} for part {part.id}")

                        # Now process new attachments being uploaded
                        for part_attachment in request.files.getlist(f'part_{part.id}_attachment'):
                            if part_attachment and part_attachment.filename != '' and allowed_file(part_attachment.filename, ALLOWED_ATTACHMENTS):
                                # Save the new attachment file
                                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                                att_filename = secure_filename(f"p_att_{timestamp}_{part_attachment.filename}")
                                part_attachment.save(os.path.join(upload_folder, att_filename))

                                # Track this new filename
                                newly_uploaded_filenames.append(att_filename)

                                # Create a new attachment record in the database
                                new_attachment = Attachment(
                                    filename=att_filename,
                                    part_id=part.id
                                )
                                db.session.add(new_attachment)
                                app_logger.info(f"Added new attachment {att_filename} for part {part.id}")
                            elif part_attachment and part_attachment.filename != '':
                                flash(f"Invalid file type for part {part.id} attachment.", "warning")

                        # Commit the changes to ensure new attachments are saved
                        db.session.flush()

                        # --- Update Marking Points for this Part ---
                        mp_ids_in_form = set()
                        mp_data_from_form = {} # Store description, score, order

                        # Extract marking point data from form
                        for mp_key in request.form:
                             mp_match = re.match(rf'part_{part.id}_mp_(\d+)_description', mp_key)
                             if mp_match:
                                 mp_id = int(mp_match.group(1))
                                 mp_ids_in_form.add(mp_id)
                                 mp_data_from_form[mp_id] = {
                                     'description': request.form.get(mp_key, ''),
                                     'score': request.form.get(f'part_{part.id}_mp_{mp_id}_score', '1.0'),
                                     'order': request.form.get(f'part_{part.id}_mp_{mp_id}_order', '0') # Assuming order is submitted
                                 }

                        # Update existing MPs and delete removed ones
                        mps_to_delete = []
                        for mp in part.marking_points:
                             if mp.id in mp_ids_in_form:
                                 # Update existing MP
                                 data = mp_data_from_form[mp.id]
                                 mp.description = data['description']
                                 try: mp.score = float(data['score'])
                                 except ValueError: mp.score = 1.0
                                 try: mp.order = int(data['order'])
                                 except ValueError: mp.order = 0 # Default order
                             else:
                                 # Mark MP for deletion
                                 mps_to_delete.append(mp)

                        # Delete MPs marked for deletion
                        for mp_del in mps_to_delete:
                             db.session.delete(mp_del)

                        # Add new MPs (those in form but not in DB yet - requires identifying new ones)
                        # This part is tricky without a clear way to identify new MPs in the form.
                        # A common pattern is to delete all existing MPs and recreate them from the form data.
                        # Let's adopt that pattern for simplicity here:

                        # Get marking points from the form
                        marking_points_descriptions = request.form.getlist(f'part_{part.id}_marking_points')
                        marking_points_scores = request.form.getlist(f'part_{part.id}_marking_scores')

                        # Delete all existing marking points for the part
                        MarkingPoint.query.filter_by(part_id=part.id).delete()
                        db.session.flush() # Ensure deletes happen before adds

                        # Add new marking points from form data
                        for idx, (description, score_str) in enumerate(zip(marking_points_descriptions, marking_points_scores)):
                            if description.strip():  # Only add if description exists
                                try:
                                    score = float(score_str)
                                except ValueError:
                                    score = 1.0

                                new_mp = MarkingPoint(
                                    part_id=part.id,
                                    description=description,
                                    score=score,
                                    order=idx,
                                    is_auto_generated=False
                                )
                                db.session.add(new_mp)
                        # --- End Re-add Marking Points ---

                        # --- Handle Misconceptions ---
                        # Get misconceptions from form
                        misconceptions_list = request.form.getlist(f'part_{part.id}_misconceptions')
                        # Filter out empty misconceptions
                        misconceptions = [m.strip() for m in misconceptions_list if m.strip()]

                        # Update misconceptions in JSON file
                        from misconception_utils import update_part_misconceptions
                        update_part_misconceptions(question_id, part.id, misconceptions)
                        # --- End Handle Misconceptions ---

                    else:
                        # Mark part for deletion if not in form
                        parts_to_delete.append(part)

                # Delete parts marked for deletion
                for part_del in parts_to_delete:
                    # Also delete associated marking points first
                    MarkingPoint.query.filter_by(part_id=part_del.id).delete()
                    db.session.delete(part_del)

                # Add new parts (identified by index 'new' in form?)
                # Similar to marking points, handling dynamic additions needs a clear strategy.
                # Assuming new parts are added via a separate mechanism or identified clearly in the form.
                # Placeholder for adding new parts if form structure supports it.

                db.session.commit()
                flash('Question updated successfully!', 'success')
                app_logger.info(f"Admin {session.get('username')} updated question ID: {question_id}")
                return redirect(url_for('load_question', question_id=question.id)) # Redirect to view question

            except Exception as e:
                db.session.rollback()
                error_logger.exception(f"Error updating question {question_id}: {e}")
                flash(f"An error occurred while updating the question: {str(e)}", "error")
                # Stay on edit page on error
                subjects = Subject.query.order_by(Subject.name).all()
                # Reload topics for the current subject if topic exists
                if question.topic:
                    topics = Topic.query.filter_by(subject_id=question.topic.subject_id).order_by(Topic.name).all()
                else:
                    # If no topic is set, just load the first subject's topics
                    first_subject = Subject.query.first()
                    topics = Topic.query.filter_by(subject_id=first_subject.id).order_by(Topic.name).all() if first_subject else []

                # Load misconceptions for error case too
                from misconception_utils import get_all_question_misconceptions
                question_misconceptions = get_all_question_misconceptions(question_id)

                return render_template('edit_question.html', question=question, subjects=subjects, topics=topics, question_misconceptions=question_misconceptions)


        # GET request - show edit form
        subjects = Subject.query.order_by(Subject.name).all()

        # Load topics for the question's current subject to populate dropdown
        if question.topic:
            # If question has a topic, load topics for that subject
            topics = Topic.query.filter_by(subject_id=question.topic.subject_id).order_by(Topic.name).all()
        else:
            # If no topic is set, just load the first subject's topics
            first_subject = Subject.query.first()
            topics = Topic.query.filter_by(subject_id=first_subject.id).order_by(Topic.name).all() if first_subject else []

        # Load misconceptions for all parts of this question
        from misconception_utils import get_all_question_misconceptions
        question_misconceptions = get_all_question_misconceptions(question_id)

        return render_template('edit_question.html', question=question, subjects=subjects, topics=topics, question_misconceptions=question_misconceptions)

    @app.route('/question/<int:question_id>/submissions')
    @admin_required
    def question_submissions(question_id):
        """Display all submissions for a specific question (admin only)."""
        try:
            # Get the question with error handling
            question = Question.query.get_or_404(question_id)

            # Get all submissions for this question with user information
            # Join with User and Part to get all necessary information
            submissions = db.session.query(Submission, User, Part).join(
                User, Submission.user_id == User.id
            ).join(
                Part, Submission.part_id == Part.id
            ).filter(
                Submission.question_id == question_id
            ).order_by(
                Submission.timestamp.desc()
            ).all()

            # Group submissions by part for better organization
            submissions_by_part = {}
            for submission, user, part in submissions:
                if part.id not in submissions_by_part:
                    submissions_by_part[part.id] = {
                        'part': part,
                        'submissions': []
                    }
                submissions_by_part[part.id]['submissions'].append({
                    'submission': submission,
                    'user': user
                })

            # Get submission statistics
            total_submissions = len(submissions)
            unique_users = len(set(user.id for _, user, _ in submissions))

            # Calculate average score per part
            part_stats = {}
            for part_id, data in submissions_by_part.items():
                part_submissions = data['submissions']
                if part_submissions:
                    scores = [s['submission'].score for s in part_submissions if s['submission'].score is not None]
                    part_stats[part_id] = {
                        'total_submissions': len(part_submissions),
                        'avg_score': sum(scores) / len(scores) if scores else 0,
                        'max_score': data['part'].score
                    }

            return render_template('question_submissions.html',
                                 question=question,
                                 submissions_by_part=submissions_by_part,
                                 total_submissions=total_submissions,
                                 unique_users=unique_users,
                                 part_stats=part_stats)

        except Exception as e:
            error_logger.exception(f"Error loading submissions for question {question_id}")
            flash("Error loading submissions for this question.", "error")
            return redirect(url_for('load_question', question_id=question_id))

    @app.route('/delete_question/<int:question_id>')
    @admin_required
    def delete_question(question_id):
        question = Question.query.get_or_404(question_id)
        parts_to_delete = []
        attachhments_to_delete = []
        for parts in question.parts:
            parts_to_delete.append(parts)

            for attachments in parts.attachments:
                attachhments_to_delete.append(attachments)

        for part in parts_to_delete:
            for attachment in attachhments_to_delete:
                db.session.delete(attachment)
            try:
                db.session.delete(part)
            except Exception as e:
                print(f"Error deleting part {part.id}: {e}")

        try:
            db.session.delete(question)
            db.session.commit()
        except Exception as e:
            print(f"Error deleting question {question_id}: {e}")

        app.logger.info(f"Question {question_id} deleted by admin {session.get('username')}")

        flash("Question deleted successfully!", "success")
        return redirect(url_for('index'))

    # --- Helper Endpoint for Dynamic Topics ---
    @app.route('/get_topics/<int:subject_id>', methods=['GET'])
    @login_required # Changed from admin_required to login_required so all users can access topics
    def get_topics(subject_id):
        """Returns topics for a given subject ID as JSON."""
        try:
            subject = Subject.query.get(subject_id)
            if not subject:
                 return jsonify({'error': 'Subject not found'}), 404

            topics = Topic.query.filter_by(subject_id=subject_id).order_by(Topic.name).all()

            # Get current user for progress calculation
            from flask import session
            user_id = session.get('user_id')

            # Add dojo question counts and progress for each topic
            topics_data = []
            for topic in topics:
                # Get dojo questions for this topic
                dojo_question_count = Question.query.filter_by(
                    topic_id=topic.id,
                    is_dojo=True
                ).count()

                completed_count = 0
                attempted_count = 0
                progress_percentage = 0

                if user_id and dojo_question_count > 0:
                    try:
                        # Get dojo questions for this topic
                        dojo_questions = Question.query.filter_by(
                            topic_id=topic.id,
                            is_dojo=True
                        ).all()

                        # Get user's best scores for these questions
                        from sqlalchemy import func
                        question_ids = [q.id for q in dojo_questions]

                        best_scores = db.session.query(
                            Submission.question_id,
                            func.max(Submission.score).label('best_score')
                        ).filter(
                            Submission.user_id == user_id,
                            Submission.question_id.in_(question_ids)
                        ).group_by(Submission.question_id).all()

                        best_scores_dict = {q_id: score for q_id, score in best_scores}

                        # Calculate completion status
                        for question in dojo_questions:
                            total_possible = sum(part.score for part in question.parts)
                            best_score = best_scores_dict.get(question.id, 0)

                            if best_score > 0:
                                attempted_count += 1
                                if total_possible > 0:
                                    question_progress = (best_score / total_possible) * 100
                                    if question_progress >= 80:  # 80%+ considered completed
                                        completed_count += 1

                        # Calculate overall progress percentage
                        progress_percentage = (completed_count / dojo_question_count * 100) if dojo_question_count > 0 else 0

                    except Exception as e:
                        error_logger.error(f"Error calculating progress for topic {topic.id}: {e}")
                        # Continue with default values

                topics_data.append({
                    'id': topic.id,
                    'name': topic.name,
                    'dojo_question_count': dojo_question_count,
                    'completed_count': completed_count,
                    'attempted_count': attempted_count,
                    'progress_percentage': round(progress_percentage, 1)
                })

            return jsonify({'topics': topics_data})
        except Exception as e:
             error_logger.error(f"Error fetching topics for subject {subject_id}: {e}")
             return jsonify({'error': 'Could not fetch topics'}), 500

    # --- Dojo Management Routes ---
    @app.route('/admin/dojo', methods=['GET'])
    @admin_required
    def admin_dojo():
        """Admin page for managing dojo questions and prerequisites."""
        try:
            subjects = Subject.query.order_by(Subject.name).all()
            # Get all questions with their dojo status
            questions = Question.query.join(Topic).join(Subject).order_by(Subject.name, Topic.name, Question.title).all()
            return render_template("admin_dojo.html", title="Dojo Management", subjects=subjects, questions=questions)
        except Exception as e:
            error_logger.exception("Error loading dojo management page")
            flash("Error loading dojo management page.", "error")
            return redirect(url_for('admin'))

    @app.route('/admin/dojo/toggle/<int:question_id>', methods=['POST'])
    @admin_required
    def toggle_dojo_question(question_id):
        """Toggle a question's dojo status."""
        try:
            question = Question.query.get_or_404(question_id)
            question.is_dojo = not question.is_dojo
            db.session.commit()

            status = "enabled" if question.is_dojo else "disabled"
            flash(f"Dojo status {status} for question '{question.title}'", "success")
            app_logger.info(f"Admin {session.get('username')} {status} dojo for question ID: {question_id}")

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error toggling dojo status for question {question_id}")
            flash("Error updating question dojo status.", "error")

        return redirect(url_for('admin_dojo'))

    @app.route('/admin/dojo/prerequisites/<int:question_id>', methods=['GET', 'POST'])
    @admin_required
    def manage_prerequisites(question_id):
        """Manage prerequisites for a specific question."""
        question = Question.query.get_or_404(question_id)

        if request.method == 'POST':
            try:
                # Get selected prerequisite question IDs
                prerequisite_ids = request.form.getlist('prerequisites')
                prerequisite_ids = [int(pid) for pid in prerequisite_ids if pid.isdigit()]

                # Remove existing prerequisites
                QuestionPrerequisite.query.filter_by(question_id=question_id).delete()

                # Add new prerequisites
                for prereq_id in prerequisite_ids:
                    if prereq_id != question_id:  # Prevent self-reference
                        prerequisite = QuestionPrerequisite(
                            question_id=question_id,
                            prerequisite_question_id=prereq_id
                        )
                        db.session.add(prerequisite)

                db.session.commit()
                flash(f"Prerequisites updated for question '{question.title}'", "success")
                app_logger.info(f"Admin {session.get('username')} updated prerequisites for question ID: {question_id}")

                return redirect(url_for('admin_dojo'))

            except Exception as e:
                db.session.rollback()
                error_logger.exception(f"Error updating prerequisites for question {question_id}")
                flash("Error updating prerequisites.", "error")

        # Get current prerequisites
        current_prerequisites = [p.prerequisite_question_id for p in question.prerequisites]

        # Get all dojo questions from the same subject (potential prerequisites)
        potential_prerequisites = Question.query.join(Topic).filter(
            Topic.subject_id == question.topic.subject_id,
            Question.is_dojo == True,
            Question.id != question_id
        ).order_by(Topic.name, Question.title).all()

        return render_template("admin_prerequisites.html",
                             question=question,
                             potential_prerequisites=potential_prerequisites,
                             current_prerequisites=current_prerequisites)

    @app.route('/admin/dojo/questions/<int:subject_id>', methods=['GET'])
    @admin_required
    def get_dojo_questions(subject_id):
        """Get dojo questions for a specific subject as JSON."""
        try:
            questions = Question.query.join(Topic).filter(
                Topic.subject_id == subject_id,
                Question.is_dojo == True
            ).order_by(Topic.name, Question.title).all()

            questions_data = []
            for q in questions:
                questions_data.append({
                    'id': q.id,
                    'title': q.title,
                    'topic': q.topic.name,
                    'prerequisites': [p.prerequisite_question_id for p in q.prerequisites]
                })

            return jsonify({'questions': questions_data})

        except Exception as e:
            error_logger.error(f"Error fetching dojo questions for subject {subject_id}: {e}")
            return jsonify({'error': 'Could not fetch dojo questions'}), 500

    # --- Question Relevance Management Routes ---
    @app.route('/admin/question/relevance/<int:question_id>', methods=['GET', 'POST'])
    @admin_required
    def manage_question_relevance(question_id):
        """Manage relevance relationships for a specific question."""
        question = Question.query.get_or_404(question_id)

        if request.method == 'POST':
            try:
                # Get selected relevant question IDs
                relevant_ids = request.form.getlist('relevant_questions')
                relevant_ids = [int(rid) for rid in relevant_ids if rid.isdigit()]

                # Get relevance types and strengths
                relevance_types = {}
                relevance_strengths = {}
                for rid in relevant_ids:
                    relevance_types[rid] = request.form.get(f'relevance_type_{rid}', 'general')
                    relevance_strengths[rid] = int(request.form.get(f'relevance_strength_{rid}', 1))

                # Remove existing relevance relationships
                QuestionRelevance.query.filter_by(question_id=question_id).delete()

                # Add new relevance relationships
                for relevant_id in relevant_ids:
                    if relevant_id != question_id:  # Prevent self-reference
                        relevance = QuestionRelevance(
                            question_id=question_id,
                            relevant_question_id=relevant_id,
                            relevance_type=relevance_types[relevant_id],
                            strength=relevance_strengths[relevant_id]
                        )
                        db.session.add(relevance)

                db.session.commit()
                flash(f"Relevance relationships updated for '{question.title}'.", "success")
                app_logger.info(f"Admin {session.get('username')} updated relevance for question {question_id}")
                return redirect(url_for('manage_question_relevance', question_id=question_id))

            except Exception as e:
                db.session.rollback()
                error_logger.exception(f"Error updating relevance for question {question_id}")
                flash("Error updating relevance relationships.", "error")

        # Get current relevance relationships
        current_relevance = {}
        for rel in question.relevant_questions:
            current_relevance[rel.relevant_question_id] = {
                'type': rel.relevance_type,
                'strength': rel.strength
            }

        # Get all questions from the same subject (potential relevant questions)
        potential_relevant = Question.query.join(Topic).filter(
            Topic.subject_id == question.topic.subject_id,
            Question.id != question_id
        ).order_by(Topic.name, Question.title).all()

        return render_template("admin_question_relevance.html",
                             question=question,
                             potential_relevant=potential_relevant,
                             current_relevance=current_relevance)

    @app.route('/admin/questions/<int:subject_id>/relevance', methods=['GET'])
    @admin_required
    def get_questions_for_relevance(subject_id):
        """Get questions for a specific subject as JSON for relevance management."""
        try:
            questions = Question.query.join(Topic).filter(
                Topic.subject_id == subject_id
            ).order_by(Topic.name, Question.title).all()

            questions_data = []
            for q in questions:
                relevance_data = []
                for rel in q.relevant_questions:
                    relevance_data.append({
                        'question_id': rel.relevant_question_id,
                        'type': rel.relevance_type,
                        'strength': rel.strength
                    })

                questions_data.append({
                    'id': q.id,
                    'title': q.title,
                    'topic': q.topic.name,
                    'relevance': relevance_data
                })

            return jsonify({'questions': questions_data})

        except Exception as e:
            error_logger.error(f"Error fetching questions for relevance in subject {subject_id}: {e}")
            return jsonify({'error': 'Could not fetch questions'}), 500

    @app.route('/admin/manage_notes_relevance')
    @admin_required
    def manage_notes_relevance():
        """Manage question-notes relevance relationships."""
        # Get filter parameters
        subject_id = request.args.get('subject_id', type=int)
        topic_id = request.args.get('topic_id', type=int)
        search = request.args.get('search', '').strip()

        # Base query for questions
        questions_query = Question.query

        # Apply filters
        if subject_id:
            questions_query = questions_query.filter_by(subject_id=subject_id)
        if topic_id:
            questions_query = questions_query.filter_by(topic_id=topic_id)
        if search:
            questions_query = questions_query.filter(
                Question.title.contains(search) |
                Question.description.contains(search)
            )

        # Get questions with their notes relevance
        questions = questions_query.order_by(Question.id.desc()).limit(50).all()

        # Get all subjects and topics for filters
        subjects = Subject.query.all()
        topics = Topic.query.all()

        # Get all notes chunks for selection
        notes_chunks = NotesChunk.query.order_by(NotesChunk.filename, NotesChunk.title).all()

        return render_template('admin/manage_notes_relevance.html',
                             questions=questions,
                             subjects=subjects,
                             topics=topics,
                             notes_chunks=notes_chunks,
                             current_subject_id=subject_id,
                             current_topic_id=topic_id,
                             current_search=search)

    @app.route('/admin/api/add_notes_relevance/<int:question_id>', methods=['POST'])
    @admin_required
    def add_notes_relevance(question_id):
        """Add a notes relevance relationship."""
        try:
            notes_chunk_id = request.form.get('notes_chunk_id', type=int)
            relevance_type = request.form.get('relevance_type')
            strength = request.form.get('strength', type=int)

            if not all([notes_chunk_id, relevance_type, strength]):
                return "Missing required fields", 400

            # Check if relationship already exists
            existing = QuestionNotesRelevance.query.filter_by(
                question_id=question_id,
                notes_chunk_id=notes_chunk_id
            ).first()

            if existing:
                return "Relevance relationship already exists", 400

            # Create new relationship
            relevance = QuestionNotesRelevance(
                question_id=question_id,
                notes_chunk_id=notes_chunk_id,
                relevance_type=relevance_type,
                strength=strength
            )

            db.session.add(relevance)
            db.session.commit()

            app_logger.info(f"Admin {session['user_id']} added notes relevance: Q{question_id} -> Chunk{notes_chunk_id}")
            return "Success", 200

        except Exception as e:
            db.session.rollback()
            error_logger.error(f"Error adding notes relevance: {e}")
            return f"Error: {str(e)}", 500

    @app.route('/admin/api/remove_notes_relevance/<int:relevance_id>', methods=['DELETE'])
    @admin_required
    def remove_notes_relevance(relevance_id):
        """Remove a notes relevance relationship."""
        try:
            relevance = QuestionNotesRelevance.query.get_or_404(relevance_id)

            question_id = relevance.question_id
            chunk_id = relevance.notes_chunk_id

            db.session.delete(relevance)
            db.session.commit()

            app_logger.info(f"Admin {session['user_id']} removed notes relevance: Q{question_id} -> Chunk{chunk_id}")
            return "Success", 200

        except Exception as e:
            db.session.rollback()
            error_logger.error(f"Error removing notes relevance: {e}")
            return f"Error: {str(e)}", 500

    @app.route('/admin/populate_notes_relevance')
    @admin_required
    def populate_notes_relevance():
        """Auto-populate notes relevance using RAG system."""
        return render_template('admin/populate_notes_relevance.html')

    # Global dictionary to store regrade task progress
    regrade_tasks = {}

    @app.route('/admin/regrade_all_submissions/<int:question_id>', methods=['POST'])
    @admin_required
    def regrade_all_submissions(question_id):
        """Start regrading all submissions for a specific question."""
        try:
            # Verify question exists
            question = Question.query.get_or_404(question_id)

            # Get all submissions for this question
            submissions = Submission.query.filter_by(question_id=question_id).all()

            if not submissions:
                return jsonify({'status': 'error', 'message': 'No submissions found for this question'}), 400

            # Create a unique task ID
            import uuid
            task_id = str(uuid.uuid4())

            # Initialize task progress
            regrade_tasks[task_id] = {
                'status': 'running',
                'total': len(submissions),
                'completed': 0,
                'results': [],
                'latest_results': [],
                'question_id': question_id,
                'started_at': datetime.now()
            }

            # Start regrading in background thread
            from threading import Thread
            thread = Thread(target=_regrade_submissions_background, args=(task_id, submissions))
            thread.daemon = True
            thread.start()

            app_logger.info(f"Admin {session['user_id']} started regrading {len(submissions)} submissions for question {question_id}")

            return jsonify({
                'status': 'success',
                'task_id': task_id,
                'total_submissions': len(submissions)
            })

        except Exception as e:
            error_logger.exception(f"Error starting regrade for question {question_id}: {str(e)}")
            return jsonify({'status': 'error', 'message': 'Failed to start regrading process'}), 500

    @app.route('/admin/regrade_progress/<task_id>')
    @admin_required
    def regrade_progress(task_id):
        """Get progress of a regrade task."""
        if task_id not in regrade_tasks:
            return jsonify({'status': 'error', 'message': 'Task not found'}), 404

        task = regrade_tasks[task_id]

        # Get latest results and clear them (so they're only sent once)
        latest_results = task['latest_results']
        task['latest_results'] = []

        return jsonify({
            'status': task['status'],
            'total': task['total'],
            'completed': task['completed'],
            'latest_results': latest_results
        })

    def _regrade_submissions_background(task_id, submissions):
        """Background function to regrade submissions."""
        try:
            # Get the app instance
            from app import app, db
            from grading_utils import regrade_submission

            # Initialize AI clients within the background function
            import os
            from groq import Groq
            import google.generativeai as genai

            groq_client = Groq(api_key=os.getenv("GROQ_API_KEY")) if os.getenv("GROQ_API_KEY") else None
            genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
            gemini_grading_client = genai.GenerativeModel('gemini-2.5-flash')

            with app.app_context():
                # Store original submission data for rollback
                original_submissions_data = []

                for i, submission in enumerate(submissions):
                    try:
                        # Store original submission data before any changes
                        original_data = {
                            'id': submission.id,
                            'score': submission.score,
                            'feedback': submission.feedback
                        }
                        original_submissions_data.append(original_data)

                        # Store original score and get part data
                        original_score = submission.score
                        part_data = Part.query.get(submission.part_id)

                        if not part_data:
                            app_logger.warning(f"Part {submission.part_id} not found for submission {submission.id}")
                            continue

                        # Regrade the submission using the utility function
                        grading_details = regrade_submission(submission, groq_client, gemini_grading_client)

                        new_score = grading_details['score']

                        # Temporarily update submission for route testing (will be rolled back)
                        feedback_data = {
                            'evaluated_points': grading_details.get('evaluated_points', []),
                            'timing': grading_details.get('timing', {}),
                            'total_score': new_score,
                            'max_score': part_data.score,
                            'evidence_list': grading_details.get('evidence_list', []),
                            'detected_mistakes': grading_details.get('detected_mistakes', []),
                            'regraded_at': datetime.now().isoformat(),
                            'original_score': original_score
                        }

                        # Temporarily update the submission (this will be rolled back)
                        submission.score = new_score
                        submission.feedback = json.dumps(feedback_data)

                        # Get user info
                        user = User.query.get(submission.user_id)

                        # Add result to task (detailed grading will be loaded via existing routes)
                        result = {
                            'submission_id': submission.id,
                            'username': user.username if user else 'Unknown',
                            'part_number': getattr(part_data, 'part_number', submission.part_id) if part_data else submission.part_id,
                            'old_score': original_score,
                            'new_score': new_score,
                            'timestamp': datetime.now().isoformat(),
                            'answer': submission.answer,  # Full answer for the existing routes
                            'max_score': part_data.score if part_data else 0,
                            'question_id': submission.question_id,
                            'part_id': submission.part_id
                        }

                        regrade_tasks[task_id]['results'].append(result)
                        regrade_tasks[task_id]['latest_results'].append(result)
                        regrade_tasks[task_id]['completed'] = i + 1

                        app_logger.info(f"Regraded submission {submission.id}: {original_score} -> {new_score} (temporary)")

                    except Exception as e:
                        error_logger.exception(f"Error regrading submission {submission.id}: {str(e)}")
                        continue

                # IMPORTANT: Rollback all database changes to preserve original data
                try:
                    app_logger.info(f"Rolling back database changes for {len(original_submissions_data)} submissions")
                    db.session.rollback()

                    # Verify rollback by checking a few submissions
                    for original_data in original_submissions_data[:3]:  # Check first 3
                        submission = Submission.query.get(original_data['id'])
                        if submission:
                            if submission.score == original_data['score'] and submission.feedback == original_data['feedback']:
                                app_logger.info(f"✅ Rollback verified for submission {submission.id}")
                            else:
                                app_logger.warning(f"⚠️ Rollback verification failed for submission {submission.id}")

                    app_logger.info("Database rollback completed - original submission data preserved")

                except Exception as rollback_error:
                    error_logger.exception(f"Error during database rollback: {str(rollback_error)}")
                    # Try to restore manually if rollback failed
                    try:
                        for original_data in original_submissions_data:
                            submission = Submission.query.get(original_data['id'])
                            if submission:
                                submission.score = original_data['score']
                                submission.feedback = original_data['feedback']
                        db.session.commit()
                        app_logger.info("Manual restoration of original data completed")
                    except Exception as restore_error:
                        error_logger.exception(f"Critical error: Failed to restore original data: {str(restore_error)}")

                # Mark task as complete
                regrade_tasks[task_id]['status'] = 'complete'
                regrade_tasks[task_id]['completed_at'] = datetime.now()
                app_logger.info(f"Completed regrading task {task_id}")

        except Exception as e:
            error_logger.exception(f"Error in background regrade task {task_id}: {str(e)}")
            if task_id in regrade_tasks:
                regrade_tasks[task_id]['status'] = 'error'
                regrade_tasks[task_id]['message'] = str(e)

    @app.route('/admin/cleanup_regrade_tasks', methods=['POST'])
    @admin_required
    def cleanup_regrade_tasks():
        """Clean up old regrade tasks to prevent memory leaks."""
        try:
            current_time = datetime.now()
            tasks_to_remove = []

            for task_id, task_data in regrade_tasks.items():
                # Remove tasks older than 1 hour
                started_at = task_data.get('started_at', current_time)
                if (current_time - started_at).total_seconds() > 3600:
                    tasks_to_remove.append(task_id)

            for task_id in tasks_to_remove:
                del regrade_tasks[task_id]

            app_logger.info(f"Cleaned up {len(tasks_to_remove)} old regrade tasks")
            return jsonify({'status': 'success', 'cleaned_tasks': len(tasks_to_remove)})

        except Exception as e:
            error_logger.exception(f"Error cleaning up regrade tasks: {str(e)}")
            return jsonify({'status': 'error', 'message': str(e)}), 500
