{% extends "base.html" %}

{% block head %}
{{ super() }}
<!-- Add required libraries for LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js"></script>

<style>
    /* Animated background grid */
    .bg-grid-white {
        background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
    }

    /* Submission card animations */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .submission-card {
        animation: fadeInUp 0.5s ease-out forwards;
        animation-delay: calc(var(--animation-order, 0) * 0.1s);
        opacity: 0;
    }

    /* Score circle animation */
    @keyframes progressCircle {
        0% {
            stroke-dasharray: 0 100;
        }
    }

    .score-circle {
        animation: progressCircle 1s ease-out forwards;
        animation-delay: 0.5s;
    }

    /* Hover effects */
    .submission-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-perfect {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .status-partial {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
    }

    .status-zero {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }

    /* Marking point styles */
    .marking-point-achieved {
        background: linear-gradient(135deg, #ecfdf5, #d1fae5);
        border-left: 4px solid #10b981;
    }

    .marking-point-partial {
        background: linear-gradient(135deg, #fffbeb, #fef3c7);
        border-left: 4px solid #f59e0b;
    }

    .marking-point-missed {
        background: linear-gradient(135deg, #fef2f2, #fee2e2);
        border-left: 4px solid #ef4444;
    }

    /* Evidence highlighting */
    .evidence-text {
        background: rgba(99, 102, 241, 0.1);
        border: 1px solid rgba(99, 102, 241, 0.2);
        border-radius: 0.375rem;
        padding: 0.5rem;
        font-style: italic;
    }

    /* SAQ Feedback Click-to-Toggle Styles (from question.html) */
    .saq-feedback-container {
        min-height: 2rem;
        cursor: pointer;
    }

    .saq-feedback-hidden {
        transition: filter 0.3s ease-in-out;
        filter: blur(4px); /* Default blurred state */
    }

    .saq-hover-hint {
        backdrop-filter: blur(2px);
        border: 1px dashed #d1d5db;
        transition: opacity 0.3s ease-in-out;
    }

    /* Toggle states for click functionality */
    .saq-feedback-container.revealed .saq-feedback-hidden {
        filter: blur(0px) !important;
    }

    .saq-feedback-container.revealed .saq-hover-hint {
        opacity: 0 !important;
    }

    /* For regrade results, show feedback by default */
    #resultsContainer .saq-feedback-container .saq-feedback-hidden {
        filter: blur(0px) !important;
    }

    #resultsContainer .saq-feedback-container .saq-hover-hint {
        opacity: 0 !important;
    }

    /* Add subtle animation to the eye icon */
    .saq-hover-hint svg {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header with animated gradient -->
    <div class="relative bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-xl overflow-hidden mb-8 shadow-lg">
        <div class="absolute inset-0 bg-grid-white/[0.05] bg-[size:20px_20px]"></div>
        <div class="relative p-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="mb-4 lg:mb-0">
                    <h1 class="text-3xl font-bold text-white tracking-tight">Submissions Summary</h1>
                    <p class="mt-2 text-lg text-purple-100">
                        All submissions for: <span class="font-semibold text-white">{{ question.title }}</span>
                        {% if question.source %}
                            <span class="text-purple-200">from {{ question.source }}</span>
                        {% endif %}
                    </p>
                    {% if question.topic %}
                    <div class="mt-2">
                        <span class="inline-flex items-center rounded-full bg-white/20 px-3 py-1 text-sm font-medium text-white backdrop-blur-sm">
                            <i class="fas fa-tag mr-2"></i>
                            {{ question.topic.name }}
                        </span>
                    </div>
                    {% endif %}
                </div>
                <div class="flex space-x-3">
                    {% if total_submissions > 0 %}
                    <button id="regradeAllBtn"
                            class="inline-flex items-center px-4 py-2 bg-red-500/90 backdrop-blur-sm border border-red-400/50 rounded-lg text-white hover:bg-red-600/90 transition-all duration-200 font-medium">
                        <i class="fas fa-redo mr-2"></i>
                        Regrade All Submissions
                    </button>
                    {% endif %}
                    <a href="{{ url_for('load_question', question_id=question.id) }}"
                       class="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-lg text-white hover:bg-white/30 transition-all duration-200 font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Question
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
            <div class="p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-100 text-sm font-medium uppercase tracking-wide">Total Submissions</p>
                        <p class="text-3xl font-bold">{{ total_submissions }}</p>
                    </div>
                    <div class="bg-white/20 rounded-full p-3">
                        <i class="fas fa-file-alt text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
            <div class="p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-100 text-sm font-medium uppercase tracking-wide">Unique Users</p>
                        <p class="text-3xl font-bold">{{ unique_users }}</p>
                    </div>
                    <div class="bg-white/20 rounded-full p-3">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
            <div class="p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-100 text-sm font-medium uppercase tracking-wide">Question Parts</p>
                        <p class="text-3xl font-bold">{{ question.parts|length }}</p>
                    </div>
                    <div class="bg-white/20 rounded-full p-3">
                        <i class="fas fa-puzzle-piece text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
            <div class="p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-orange-100 text-sm font-medium uppercase tracking-wide">Max Score</p>
                        <p class="text-3xl font-bold">{{ question.parts | sum(attribute='score') | int }}</p>
                    </div>
                    <div class="bg-white/20 rounded-full p-3">
                        <i class="fas fa-trophy text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Submissions by Part -->
    {% if submissions_by_part %}
        {% for part_id, part_data in submissions_by_part.items() %}
        <div class="mb-12">
            <!-- Part Header -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-t-xl border-b-4 border-indigo-500 p-6">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div class="mb-4 lg:mb-0">
                        <h2 class="text-2xl font-bold text-gray-900 flex items-center">
                            <div class="bg-indigo-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">
                                {{ loop.index }}
                            </div>
                            Part {{ loop.index }}
                            <span class="ml-3 text-lg text-gray-600">({{ part_data.part.score }} marks)</span>
                        </h2>
                        <p class="mt-2 text-gray-600">{{ part_data.submissions|length }} submissions received</p>
                        <div class="mt-2 text-sm text-gray-700 bg-white rounded-lg p-3 border-l-4 border-indigo-400">
                            {{ part_data.part.description }}
                        </div>
                    </div>
                    {% if part_id in part_stats %}
                    <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                        <div class="text-center">
                            <div class="text-sm font-medium text-gray-500 uppercase tracking-wide">Average Score</div>
                            <div class="text-2xl font-bold text-indigo-600 mt-1">
                                {{ "%.1f"|format(part_stats[part_id].avg_score) }}/{{ part_stats[part_id].max_score }}
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                                {{ "%.0f"|format((part_stats[part_id].avg_score / part_stats[part_id].max_score) * 100) }}% average
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Submissions Grid -->
            <div class="bg-gray-50 rounded-b-xl p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    {% for submission_data in part_data.submissions %}
                    {% set submission = submission_data.submission %}
                    {% set user = submission_data.user %}
                    {% set feedback_data = {} %}
                    {% if submission.feedback %}
                        {% set feedback_data = submission.feedback|from_json %}
                    {% endif %}

                    <div class="submission-card bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-lg"
                         style="--animation-order: {{ loop.index0 }}">
                        <!-- Card Header -->
                        <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg">
                                        {{ user.username[0].upper() }}
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">{{ user.username }}</h4>
                                        <p class="text-sm text-gray-500">{{ submission.timestamp.strftime('%b %d, %H:%M') }}</p>
                                    </div>
                                </div>
                                <!-- Score Badge -->
                                {% if submission.score is not none %}
                                    {% if submission.score == part_data.part.score %}
                                        <span class="status-badge status-perfect">
                                            <i class="fas fa-star mr-1"></i>
                                            Perfect
                                        </span>
                                    {% elif submission.score > 0 %}
                                        <span class="status-badge status-partial">
                                            <i class="fas fa-check-circle mr-1"></i>
                                            Partial
                                        </span>
                                    {% else %}
                                        <span class="status-badge status-zero">
                                            <i class="fas fa-times-circle mr-1"></i>
                                            Incorrect
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="status-badge bg-gray-100 text-gray-600">
                                        <i class="fas fa-clock mr-1"></i>
                                        Pending
                                    </span>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Score Circle -->
                        <div class="px-6 py-4 bg-white">
                            <div class="flex items-center justify-center mb-4">
                                <div class="relative w-20 h-20">
                                    <svg class="w-full h-full transform -rotate-90" viewBox="0 0 36 36">
                                        <path class="stroke-current text-gray-200" stroke-width="3" fill="none"
                                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                        <path class="score-circle stroke-current {% if submission.score == part_data.part.score %}text-green-500{% elif submission.score > 0 %}text-amber-500{% else %}text-red-500{% endif %}"
                                              stroke-width="3" fill="none" stroke-linecap="round"
                                              stroke-dasharray="{{ (submission.score / part_data.part.score) * 100 if submission.score else 0 }}, 100"
                                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    </svg>
                                    <div class="absolute inset-0 flex items-center justify-center">
                                        <span class="text-lg font-bold {% if submission.score == part_data.part.score %}text-green-600{% elif submission.score > 0 %}text-amber-600{% else %}text-red-600{% endif %}">
                                            {{ submission.score or 0 }}/{{ part_data.part.score }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Answer Section -->
                        <div class="px-6 py-4 border-t border-gray-100">
                            <h5 class="text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                <i class="fas fa-edit mr-2 text-indigo-500"></i>
                                Student Answer
                            </h5>
                            <div class="bg-gray-50 rounded-lg p-3 border-l-4 border-indigo-400">
                                <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ submission.answer }}</p>
                            </div>
                        </div>

                        <!-- Marking Points Section -->
                        {% if part_data.part.marking_points and feedback_data.evaluated_points %}
                        <div class="px-6 py-4 border-t border-gray-100">
                            <h5 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                                <i class="fas fa-clipboard-check mr-2 text-indigo-500"></i>
                                Marking Points
                            </h5>
                            <div class="space-y-2">
                                {% for mp in part_data.part.marking_points|sort(attribute='order') %}
                                {% set evaluation = feedback_data.evaluated_points|selectattr('id', 'equalto', mp.id)|first if feedback_data.evaluated_points else None %}

                                <div class="p-3 rounded-lg {% if evaluation and evaluation.achieved %}marking-point-achieved{% elif evaluation and evaluation.partial %}marking-point-partial{% elif evaluation %}marking-point-missed{% else %}bg-gray-50 border-l-4 border-gray-300{% endif %}">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <p class="text-xs font-medium text-gray-700">{{ mp.description }}</p>
                                            {% if evaluation and evaluation.evidence %}
                                            <div class="mt-2 evidence-text">
                                                <p class="text-xs"><strong>Evidence:</strong> "{{ evaluation.evidence }}"</p>
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="ml-3 flex items-center space-x-2">
                                            {% if evaluation %}
                                                {% if evaluation.achieved %}
                                                    <i class="fas fa-check text-green-600 text-sm"></i>
                                                {% elif evaluation.partial %}
                                                    <i class="fas fa-minus text-amber-600 text-sm"></i>
                                                {% else %}
                                                    <i class="fas fa-times text-red-600 text-sm"></i>
                                                {% endif %}
                                                <span class="text-xs font-semibold {% if evaluation.achieved %}text-green-600{% elif evaluation.partial %}text-amber-600{% else %}text-red-600{% endif %}">
                                                    {{ "%.1f"|format(evaluation.achieved_score) }}/{{ "%.1f"|format(mp.score) }}
                                                </span>
                                            {% else %}
                                                <i class="fas fa-question text-gray-400 text-sm"></i>
                                                <span class="text-xs text-gray-500">{{ mp.score }}</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Action Button -->
                        <div class="px-6 py-4 bg-gray-50 border-t border-gray-100">
                            <a href="{{ url_for('submission_details', submission_id=submission.id) }}"
                               class="w-full inline-flex items-center justify-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200">
                                <i class="fas fa-eye mr-2"></i>
                                View Full Details
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl shadow-lg overflow-hidden">
            <div class="px-8 py-16 text-center">
                <div class="bg-gradient-to-br from-indigo-100 to-purple-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-inbox text-indigo-600 text-4xl"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 mb-3">No Submissions Yet</h3>
                <p class="text-gray-600 text-lg max-w-md mx-auto">No students have submitted answers to this question yet. Check back later to see student responses.</p>
                <div class="mt-8">
                    <a href="{{ url_for('load_question', question_id=question.id) }}"
                       class="inline-flex items-center px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Question
                    </a>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- Regrade Modal -->
<div id="regradeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-900">Regrade All Submissions</h3>
                <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div id="regradeContent">
                <div id="confirmationStep" class="text-center py-8">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                        <div class="flex items-center justify-center mb-4">
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-3xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-yellow-800 mb-2">Confirm Regrade Operation</h4>
                        <p class="text-yellow-700 mb-4">
                            This will regrade all <strong>{{ total_submissions }}</strong> submissions for this question.
                            This process may take several minutes and will show you the new scores compared to the original scores.
                        </p>
                        <p class="text-sm text-yellow-600">
                            <i class="fas fa-shield-alt mr-1"></i>
                            <strong>Non-destructive operation:</strong> Original submission data will be preserved. This is for comparison purposes only.
                        </p>
                    </div>

                    <div class="flex justify-center space-x-4">
                        <button id="cancelRegrade" class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                            Cancel
                        </button>
                        <button id="confirmRegrade" class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-redo mr-2"></i>
                            Start Regrading
                        </button>
                    </div>
                </div>

                <div id="progressStep" class="hidden">
                    <div class="mb-6">
                        <div class="flex justify-between text-sm text-gray-600 mb-2">
                            <span>Progress</span>
                            <span id="progressText">0 / {{ total_submissions }}</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>

                    <div id="resultsContainer" class="max-h-96 overflow-y-auto space-y-4">
                        <!-- Results will be populated here -->
                    </div>

                    <div id="completionMessage" class="hidden text-center py-4">
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <i class="fas fa-check-circle text-green-600 text-2xl mb-2"></i>
                            <h4 class="text-lg font-semibold text-green-800">Regrading Complete!</h4>
                            <p class="text-green-700">All submissions have been regraded successfully.</p>
                            <button id="refreshPage" class="mt-4 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                Refresh Page
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation order to submission cards
        const cards = document.querySelectorAll('.submission-card');
        cards.forEach((card, index) => {
            card.style.setProperty('--animation-order', index);
        });

        // Render LaTeX in content
        if (typeof renderMathInElement !== 'undefined') {
            renderMathInElement(document.body, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false},
                    {left: '\\(', right: '\\)', display: false},
                    {left: '\\[', right: '\\]', display: true}
                ],
                throwOnError: false
            });
        }

        // Add hover effects to statistics cards
        const statCards = document.querySelectorAll('.bg-gradient-to-br');
        statCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05) translateY(-2px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) translateY(0)';
            });
        });

        // Smooth scroll to parts
        const partHeaders = document.querySelectorAll('[data-part-id]');
        partHeaders.forEach(header => {
            header.addEventListener('click', function() {
                this.scrollIntoView({ behavior: 'smooth', block: 'start' });
            });
        });

        // Regrade functionality
        const regradeBtn = document.getElementById('regradeAllBtn');
        const modal = document.getElementById('regradeModal');
        const closeModal = document.getElementById('closeModal');
        const cancelRegrade = document.getElementById('cancelRegrade');
        const confirmRegrade = document.getElementById('confirmRegrade');
        const refreshPage = document.getElementById('refreshPage');

        if (regradeBtn) {
            regradeBtn.addEventListener('click', function() {
                modal.classList.remove('hidden');
                document.getElementById('confirmationStep').classList.remove('hidden');
                document.getElementById('progressStep').classList.add('hidden');
            });
        }

        closeModal.addEventListener('click', function() {
            modal.classList.add('hidden');
        });

        cancelRegrade.addEventListener('click', function() {
            modal.classList.add('hidden');
        });

        refreshPage.addEventListener('click', function() {
            window.location.reload();
        });

        confirmRegrade.addEventListener('click', function() {
            startRegrading();
        });

        function startRegrading() {
            document.getElementById('confirmationStep').classList.add('hidden');
            document.getElementById('progressStep').classList.remove('hidden');

            const questionId = {{ question.id }};

            fetch(`/admin/regrade_all_submissions/${questionId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    pollRegradeProgress(data.task_id);
                } else {
                    showError(data.message || 'Failed to start regrading');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('An error occurred while starting the regrade process');
            });
        }

        function pollRegradeProgress(taskId) {
            const interval = setInterval(() => {
                fetch(`/admin/regrade_progress/${taskId}`)
                .then(response => response.json())
                .then(data => {
                    updateProgress(data);

                    if (data.status === 'complete') {
                        clearInterval(interval);
                        showCompletion();
                    } else if (data.status === 'error') {
                        clearInterval(interval);
                        showError(data.message || 'Regrading failed');
                    }
                })
                .catch(error => {
                    console.error('Error polling progress:', error);
                    clearInterval(interval);
                    showError('Lost connection to server');
                });
            }, 1000);
        }

        function updateProgress(data) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const resultsContainer = document.getElementById('resultsContainer');

            const percentage = (data.completed / data.total) * 100;
            progressBar.style.width = percentage + '%';
            progressText.textContent = `${data.completed} / ${data.total}`;

            // Add new results
            if (data.latest_results) {
                data.latest_results.forEach(result => {
                    addResultCard(result, resultsContainer);
                });
            }
        }

        function addResultCard(result, container) {
            const card = document.createElement('div');
            card.className = 'bg-white border border-gray-200 rounded-lg shadow-sm mb-6 overflow-hidden';

            const scoreChange = result.new_score - result.old_score;
            const scoreChangeClass = scoreChange > 0 ? 'text-green-600' : scoreChange < 0 ? 'text-red-600' : 'text-gray-600';
            const scoreChangeIcon = scoreChange > 0 ? 'fa-arrow-up' : scoreChange < 0 ? 'fa-arrow-down' : 'fa-minus';

            card.innerHTML = `
                <!-- Header with user info and score change -->
                <div class="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                <span class="text-sm font-medium text-indigo-600">${result.username.charAt(0).toUpperCase()}</span>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-900">${result.username}</p>
                                <p class="text-sm text-gray-500">Part ${result.part_number} • ${new Date(result.timestamp).toLocaleTimeString()}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="text-sm text-gray-500">${result.old_score}</span>
                                <i class="fas fa-arrow-right text-gray-400"></i>
                                <span class="font-semibold text-lg">${result.new_score}</span>
                                <span class="${scoreChangeClass} font-medium">
                                    <i class="fas ${scoreChangeIcon} text-xs"></i>
                                    ${Math.abs(scoreChange).toFixed(1)}
                                </span>
                            </div>
                            <p class="text-xs text-gray-400">Score Change</p>
                        </div>
                    </div>
                </div>

                <!-- Two-column layout for highlighted answer and marking points -->
                <div class="grid grid-cols-2 gap-6 p-6" id="result-content-${result.submission_id}">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-user-edit mr-2 text-indigo-500"></i>
                            Student Answer
                        </h4>
                        <div id="highlighted-answer-${result.submission_id}" class="text-sm text-gray-700 space-y-3">
                            <div class="animate-pulse">
                                <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                            <i class="fas fa-list-check mr-2 text-green-500"></i>
                            Marking Points
                        </h4>
                        <div id="marking-points-${result.submission_id}" class="space-y-3">
                            <div class="animate-pulse">
                                <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
                                <div class="h-4 bg-gray-200 rounded w-2/3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.appendChild(card);

            // Load the detailed content immediately since we have the data
            loadResultDetails(result);

            container.scrollTop = container.scrollHeight;
        }

        async function loadResultDetails(result) {
            const highlightedAnswerDiv = document.getElementById(`highlighted-answer-${result.submission_id}`);
            const markingPointsDiv = document.getElementById(`marking-points-${result.submission_id}`);

            try {
                // Use the existing get_git_diff route to get the grading results
                const formData = new FormData();
                formData.append('answer', result.answer);

                const gitDiffResponse = await fetch(`/get_git_diff/${result.question_id}/${result.part_id}`, {
                    method: 'POST',
                    body: formData
                });

                if (!gitDiffResponse.ok) {
                    throw new Error('Failed to get grading results');
                }

                const gradingData = await gitDiffResponse.json();

                // Use the existing highlighted_answer route to get the highlighted answer
                // This route expects JSON data
                const highlightResponse = await fetch(`/highlighted_answer/${result.question_id}/${result.part_id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        answer: result.answer
                    })
                });

                let highlightedContent = '';
                if (highlightResponse.ok) {
                    highlightedContent = await highlightResponse.text();
                } else {
                    // Fallback to plain text if highlighting fails
                    highlightedContent = `<div class="text-sm text-gray-700 whitespace-pre-wrap leading-relaxed">${escapeHtml(result.answer)}</div>`;
                }

                // Update the highlighted answer section
                if (highlightedAnswerDiv) {
                    highlightedAnswerDiv.innerHTML = `
                        <div class="bg-white p-3 rounded border">
                            ${highlightedContent}
                        </div>
                    `;
                }

                // Update the marking points section using the same structure as question.html
                if (markingPointsDiv && gradingData.marking_points) {
                    const markingPointsHtml = gradingData.marking_points.map((mp, index) => {
                        // Use the same color logic as in question.html
                        const borderClass = mp.color || '';
                        const bgColorClass = borderClass ? borderClass.replace('border-', 'bg-').replace('-400', '-50') : 'bg-gray-50';
                        const iconClass = mp.achieved ? 'fa-check-circle text-green-500' : mp.partial ? 'fa-exclamation-triangle text-yellow-500' : 'fa-times-circle text-red-500';

                        return `
                            <div class="flex items-start p-3 rounded ${bgColorClass} border-l-4 ${borderClass}">
                                <!-- Vertical Color Bar -->
                                <div class="flex-shrink-0 w-1 self-stretch rounded-l-md mr-3 ${borderClass ? borderClass.replace('border-', 'bg-').replace('-400', '-300') : 'bg-transparent'}"></div>

                                <div class="flex-1">
                                    <!-- Score Display -->
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="flex items-center space-x-2">
                                            <i class="fas ${iconClass} text-sm"></i>
                                            <span class="text-xs font-medium text-gray-600">
                                                ${mp.achieved ? 'Correct' : mp.partial ? 'Partial Credit' : 'Incorrect'}
                                            </span>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">
                                            [${mp.achieved_score}/${mp.score} marks]
                                            ${mp.partial ? '<span class="ml-1 text-xs text-black font-medium">(Partial Credit)</span>' : ''}
                                        </span>
                                    </div>

                                    <!-- Feedback Section -->
                                    ${mp.feedback ? `
                                        <div class="saq-feedback-container revealed relative group mt-2" onclick="toggleFeedbackVisibility(this)">
                                            <div class="saq-feedback-hidden text-sm font-medium text-black bg-white px-3 py-2 rounded border-l-4 ${mp.achieved ? 'border-green-400' : mp.partial ? 'border-yellow-400' : 'border-red-400'} transition-all duration-300">
                                                <i class="fas ${mp.achieved ? 'fa-check-circle' : mp.partial ? 'fa-exclamation-triangle' : 'fa-times-circle'} mr-2"></i>
                                                <span class="feedback-content">${mp.feedback}</span>
                                            </div>
                                            <div class="saq-hover-hint absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-90 rounded-md opacity-0 transition-opacity duration-300 pointer-events-none">
                                                <div class="flex items-center space-x-2 text-xs text-gray-500">
                                                    <i class="fas fa-eye"></i>
                                                    <span>Click to hide feedback</span>
                                                </div>
                                            </div>
                                        </div>
                                    ` : ''}

                                    <!-- Evidence Section -->
                                    ${mp.evidence ? `
                                        <div class="mt-2 p-2 bg-white rounded border-l-2 border-blue-300">
                                            <p class="text-xs text-blue-600 font-medium mb-1">Evidence Found:</p>
                                            <p class="text-xs text-gray-700 italic">"${escapeHtml(mp.evidence)}"</p>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        `;
                    }).join('');

                    markingPointsDiv.innerHTML = `
                        <div class="space-y-3">
                            ${markingPointsHtml}
                            <div class="text-center pt-3">
                                <div class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${gradingData.score >= gradingData.max_score ? 'bg-green-100 text-green-800' : gradingData.score > 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}">
                                    Total: ${gradingData.score}/${gradingData.max_score} marks
                                </div>
                            </div>
                        </div>
                    `;
                }

            } catch (error) {
                console.error('Error loading result details:', error);

                // Show error state for highlighted answer
                if (highlightedAnswerDiv) {
                    highlightedAnswerDiv.innerHTML = `
                        <div class="bg-white p-3 rounded border">
                            <div class="text-sm text-gray-700 whitespace-pre-wrap leading-relaxed">
                                ${escapeHtml(result.answer)}
                            </div>
                            <div class="mt-2 text-xs text-red-500">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Could not load highlighting. Showing plain text.
                            </div>
                        </div>
                    `;
                }

                // Show error state for marking points
                if (markingPointsDiv) {
                    markingPointsDiv.innerHTML = `
                        <div class="text-center py-4">
                            <div class="inline-flex items-center px-3 py-2 rounded-lg bg-red-100 text-red-600">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <span class="text-sm">Error loading marking details</span>
                            </div>
                        </div>
                    `;
                }
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Add the feedback toggle function from question.html
        function toggleFeedbackVisibility(element) {
            element.classList.toggle('revealed');
        }

        function showCompletion() {
            document.getElementById('completionMessage').classList.remove('hidden');
        }

        function showError(message) {
            const resultsContainer = document.getElementById('resultsContainer');
            const errorCard = document.createElement('div');
            errorCard.className = 'bg-red-50 border border-red-200 rounded-lg p-4';
            errorCard.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                    <span class="text-red-800">${message}</span>
                </div>
            `;
            resultsContainer.appendChild(errorCard);
        }
    });
</script>
{% endblock %}
