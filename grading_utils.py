"""
Grading utilities for regrade functionality.
This module provides access to grading functions for use in admin operations.
"""

import json
from datetime import datetime
from models import Part, MarkingPoint
from routes.utils import app_logger, error_logger


def regrade_submission(submission, groq_client, gemini_grading_client):
    """
    Regrade a single submission using the current grading system.
    
    Args:
        submission: Submission object to regrade
        groq_client: Groq client for Kimi K2 grading
        gemini_grading_client: Gemini client for fallback grading
    
    Returns:
        dict: Grading results with score, feedback, etc.
    """
    try:
        # Get part data
        part_data = Part.query.get(submission.part_id)
        if not part_data:
            raise ValueError(f"Part {submission.part_id} not found")
        
        # Check if Kimi K2 is available
        def is_kimi_k2_available():
            return groq_client is not None
        
        # Import grading functions from the API module
        # Since they're defined inside register_api_routes, we need to recreate the logic
        if is_kimi_k2_available():
            grading_details = _calculate_score_and_evaluated_points_kimi_standalone(
                submission.answer, part_data, groq_client, app_logger
            )
        else:
            grading_details = _calculate_score_and_evaluated_points_standalone(
                submission.answer, part_data, gemini_grading_client, app_logger
            )
        
        return grading_details
        
    except Exception as e:
        error_logger.exception(f"Error regrading submission {submission.id}: {str(e)}")
        return {
            'score': 0,
            'evaluated_points': [],
            'evidence_list': [],
            'error': str(e),
            'timing': {}
        }


def _calculate_score_and_evaluated_points_standalone(user_answer: str, part_data: Part, gemini_model, app_logger):
    """
    Standalone version of the Gemini grading function.
    This is a simplified version that replicates the core grading logic.
    """
    try:
        # Import required modules
        from groq import Groq
        import google.generativeai as genai
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import time
        
        # Initialize timing tracker (simplified)
        start_time = time.time()
        
        total_score = 0
        evaluated_points = []
        evidence_list = []
        
        # Get marking points for this part
        marking_points = MarkingPoint.query.filter_by(part_id=part_data.id).order_by(MarkingPoint.order).all()
        
        if not marking_points:
            app_logger.warning(f"No marking points found for part {part_data.id}")
            return {
                'score': 0,
                'evaluated_points': [],
                'evidence_list': [],
                'timing': {'total_time': time.time() - start_time}
            }
        
        # Process each marking point
        for mp_index, marking_point in enumerate(marking_points):
            try:
                mp_data = {
                    'id': marking_point.id,
                    'description': marking_point.description,
                    'score': marking_point.score
                }
                
                # Simplified grading logic using Gemini
                prompt = f"""
                Evaluate if the student's answer addresses this marking point:
                
                Marking Point: {mp_data['description']}
                Student Answer: {user_answer}
                
                Respond with:
                1. CORRECT/PARTIAL/INCORRECT
                2. Evidence (quote from student answer that supports the marking point)
                3. Brief explanation
                
                Format your response as:
                STATUS: [CORRECT/PARTIAL/INCORRECT]
                EVIDENCE: [quoted text from student answer]
                EXPLANATION: [brief explanation]
                """
                
                response = gemini_model.generate_content(prompt)
                response_text = response.text
                
                # Parse response
                is_correct = "CORRECT" in response_text and "INCORRECT" not in response_text
                is_partial = "PARTIAL" in response_text
                
                # Extract evidence
                evidence_snippets = []
                if "EVIDENCE:" in response_text:
                    evidence_line = response_text.split("EVIDENCE:")[1].split("EXPLANATION:")[0].strip()
                    if evidence_line and evidence_line != "None" and evidence_line != "N/A":
                        evidence_snippets = [evidence_line]
                
                # Calculate score
                point_score = 0
                if is_correct:
                    point_score = mp_data['score']
                elif is_partial:
                    point_score = mp_data['score'] * 0.5
                
                total_score += point_score
                
                # Add to evaluated points
                evaluated_points.append({
                    'id': mp_data['id'],
                    'description': mp_data['description'],
                    'score': mp_data['score'],
                    'achieved': is_correct,
                    'partial': is_partial,
                    'achieved_score': point_score,
                    'evidence': evidence_snippets[0] if evidence_snippets else None,
                    'evidence_snippets': evidence_snippets,
                    'feedback': mp_data['description'],
                    'color': 'border-green-500' if is_correct else 'border-yellow-500' if is_partial else None,
                    'error': False,
                    'mp_index': mp_index
                })
                
                # Add to evidence list for highlighting
                if evidence_snippets:
                    evidence_list.append({
                        'evidence_snippets': evidence_snippets,
                        'color': 'border-green-500' if is_correct else 'border-yellow-500'
                    })
                
            except Exception as e:
                app_logger.exception(f"Error evaluating marking point {marking_point.id}: {str(e)}")
                # Add error entry
                evaluated_points.append({
                    'id': marking_point.id,
                    'description': marking_point.description,
                    'score': marking_point.score,
                    'achieved': False,
                    'partial': False,
                    'achieved_score': 0,
                    'evidence': None,
                    'evidence_snippets': [],
                    'feedback': marking_point.description,
                    'color': None,
                    'error': True,
                    'mp_index': mp_index
                })
        
        timing_summary = {'total_time': time.time() - start_time}
        
        return {
            'score': total_score,
            'evaluated_points': evaluated_points,
            'evidence_list': evidence_list,
            'timing': timing_summary
        }
        
    except Exception as e:
        app_logger.exception(f"Error in standalone Gemini grading: {str(e)}")
        return {
            'score': 0,
            'evaluated_points': [],
            'evidence_list': [],
            'error': str(e),
            'timing': {}
        }


def _calculate_score_and_evaluated_points_kimi_standalone(user_answer: str, part_data: Part, groq_client, app_logger):
    """
    Standalone version of the Kimi K2 grading function.
    This is a simplified version that replicates the core grading logic.
    """
    try:
        import time
        
        start_time = time.time()
        total_score = 0
        evaluated_points = []
        evidence_list = []
        
        # Get marking points for this part
        marking_points = MarkingPoint.query.filter_by(part_id=part_data.id).order_by(MarkingPoint.order).all()
        
        if not marking_points:
            app_logger.warning(f"No marking points found for part {part_data.id}")
            return {
                'score': 0,
                'evaluated_points': [],
                'evidence_list': [],
                'timing': {'total_time': time.time() - start_time}
            }
        
        # Process each marking point using Kimi K2
        for mp_index, marking_point in enumerate(marking_points):
            try:
                mp_data = {
                    'id': marking_point.id,
                    'description': marking_point.description,
                    'score': marking_point.score
                }
                
                # Simplified Kimi K2 grading logic
                prompt = f"""
                Evaluate the student's answer against this marking point:
                
                Marking Point: {mp_data['description']}
                Student Answer: {user_answer}
                
                Provide:
                1. EVIDENCE: Quote exact text from the student answer that demonstrates they addressed this point
                2. MISSED: Quote text that should have been included but is missing
                3. STATUS: CORRECT (if fully addressed), PARTIAL (if partially addressed), or INCORRECT (if not addressed)
                
                Format:
                EVIDENCE: [exact quotes from student answer]
                MISSED: [missing content]
                STATUS: [CORRECT/PARTIAL/INCORRECT]
                """
                
                response = groq_client.chat.completions.create(
                    model="moonshotai/kimi-k2-instruct",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1,
                    max_tokens=1000
                )
                
                response_text = response.choices[0].message.content
                
                # Parse response
                evidence_snippets = []
                missed_snippets = []
                
                if "EVIDENCE:" in response_text:
                    evidence_line = response_text.split("EVIDENCE:")[1].split("MISSED:")[0].strip()
                    if evidence_line and evidence_line not in ["None", "N/A", ""]:
                        evidence_snippets = [evidence_line]
                
                if "MISSED:" in response_text:
                    missed_line = response_text.split("MISSED:")[1].split("STATUS:")[0].strip()
                    if missed_line and missed_line not in ["None", "N/A", ""]:
                        missed_snippets = [missed_line]
                
                # Determine status
                is_correct = not missed_snippets and evidence_snippets
                is_partial = missed_snippets and evidence_snippets
                
                # Calculate score
                point_score = 0
                if is_correct:
                    point_score = mp_data['score']
                elif is_partial:
                    point_score = mp_data['score'] * 0.5
                
                total_score += point_score
                
                # Add to evaluated points
                evaluated_points.append({
                    'id': mp_data['id'],
                    'description': mp_data['description'],
                    'score': mp_data['score'],
                    'achieved': is_correct,
                    'partial': is_partial,
                    'achieved_score': point_score,
                    'evidence': evidence_snippets[0] if evidence_snippets else None,
                    'evidence_snippets': evidence_snippets,
                    'missed_snippets': missed_snippets,
                    'feedback': mp_data['description'],
                    'color': 'border-green-500' if is_correct else 'border-yellow-500' if is_partial else None,
                    'error': False,
                    'mp_index': mp_index
                })
                
                # Add to evidence list
                if evidence_snippets:
                    evidence_list.append({
                        'evidence_snippets': evidence_snippets,
                        'color': 'border-green-500' if is_correct else 'border-yellow-500'
                    })
                
            except Exception as e:
                app_logger.exception(f"Error evaluating marking point {marking_point.id} with Kimi K2: {str(e)}")
                # Add error entry
                evaluated_points.append({
                    'id': marking_point.id,
                    'description': marking_point.description,
                    'score': marking_point.score,
                    'achieved': False,
                    'partial': False,
                    'achieved_score': 0,
                    'evidence': None,
                    'evidence_snippets': [],
                    'missed_snippets': [],
                    'feedback': marking_point.description,
                    'color': None,
                    'error': True,
                    'mp_index': mp_index
                })
        
        timing_summary = {'total_time': time.time() - start_time}
        
        return {
            'score': total_score,
            'evaluated_points': evaluated_points,
            'evidence_list': evidence_list,
            'timing': timing_summary
        }
        
    except Exception as e:
        app_logger.exception(f"Error in standalone Kimi K2 grading: {str(e)}")
        return {
            'score': 0,
            'evaluated_points': [],
            'evidence_list': [],
            'error': str(e),
            'timing': {}
        }
